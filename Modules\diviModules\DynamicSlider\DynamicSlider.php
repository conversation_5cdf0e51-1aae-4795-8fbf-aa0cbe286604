<?php
/**
 * Module Name: Dynamic Slider
 * Description: A module for showing a slider using custom fields as image sources.
 * Version: 7.1.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

class DSTweaks_Divi_Module_DynamicSlider extends ET_Builder_Module_Slider {
    public $vb_support = 'on';

    function init() {
        parent::init();
		$this->name            = esc_html__( 'DST Dynamic Slider', 'dstweaks' );
		$this->plural          = esc_html__( 'DST Dynamic Sliders', 'dstweaks' );
		$this->child_item_text = esc_html__( 'Dynamic Slide', 'dstweaks' );
    }

    public function render($attrs, $content = null, $render_slug) {
        // Let the original module render first
        $output = parent::render($attrs, $content, $render_slug);

        // Strip the first outer <div> wrapper (generic, flexible)
        if (preg_match('/^<div[^>]*>(.*)<\/div>\s*$/s', $output, $matches)) {
            $output = $matches[1];
        }

        // Replace placeholder image URLs like http://867 or https://867
        $output = preg_replace_callback(
            '#https?://(\d+)\b#',
            function ($matches) {
                $attachment_id = intval($matches[1]);
                $url = wp_get_attachment_url($attachment_id);
                return $url ?: $matches[0];
            },
            $output
        );

        return $output;
    }
}