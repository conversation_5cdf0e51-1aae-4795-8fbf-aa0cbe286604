<?php
/**
 * Module Name: DSTweaks Persian and RTL Fixes Module
 * Description: A module for handling Persian language and RTL-specific functionality
 * Version: 4.1.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

class DSTweaks_PersianRTLFixes_Module {
    public function __construct() {
        if (!is_admin()) {
            add_action('wp_enqueue_scripts', array($this, 'dstweaks_enqueue_assets'), 999);
            add_action('init', array($this, 'dstweaks_init_functions'));
        }
    }

    public function dstweaks_is_divi_builder_available() {
        $theme = wp_get_theme();
        if ( !function_exists('is_plugin_active') ) {
                require_once ABSPATH . 'wp-admin/includes/plugin.php';
            }
            return $theme->get('Name') === 'Divi' || is_plugin_active('et-builder/et-builder.php');
    }

    public function dstweaks_enqueue_assets() {
        // Only load RTL fixes when in RTL mode
        if (!is_rtl()) {
            return;
        }

        // Register and enqueue inline style
        wp_register_style('dstweaks-inline-style', false);
        wp_enqueue_style('dstweaks-inline-style');

        // Check if divi builder is active or not, if it is, load after Divi scripts
        if ( $this -> dstweaks_is_divi_builder_available() ) {
            wp_dequeue_style('divi-style');
            wp_dequeue_style('divi-style-parent');
            wp_register_script('dstweaks-inline-script', false, array('jquery', 'et-builder-modules-script'), null, true);
        } else {
            wp_register_script('dstweaks-inline-script', false, array('jquery'), null, true);
        }
        wp_enqueue_script('dstweaks-inline-script');

/* === CSS CODE STARTS HERE (Put the code between "<<<CSS" and "CSS") === */ 
wp_add_inline_style('dstweaks-inline-style', <<<CSS
    /* Global RTL Fixes */
    .rtl {
        direction: rtl;
        unicode-bidi: embed;
    }
    .rtl input[type="text"],
    .rtl textarea {
        text-align: right;
    }
    /* Number handling in RTL context */
    .rtl .number {
        unicode-bidi: embed;
    }
    /* Menu fixes */
    .rtl .et-menu .menu-item-has-children>a:first-child:after {
        right: auto !important;
        left: 0;
    }
    .rtl .et-menu .menu-item-has-children>a:first-child {
        padding-right: unset !important;
        padding-left: 20px;
    }
    @media (max-width: 980px) {
        .non-collapsable-menu .et_pb_menu__menu {
            display: flex !important;
        }
        .non-collapsable-menu .et_mobile_nav_menu {
            display: none;
        }
        .rtl .et_pb_menu .et_pb_menu__menu > nav > ul {
            flex-direction: row-reverse;
        }
    }
    /* Footer Menu Fix */
    .rtl .vertical-menu .et_pb_menu__menu > nav > ul {
        flex-direction: column;
        width: 100%;
        align-items: flex-end;
    }
    .rtl .vertical-menu .et_pb_menu__menu > nav > ul > li {
        display: block;
        width: 100%;
        margin: 10px 0;
        text-align: right;
    }
    /* Mobile Layout Fixes */
    @media (max-width: 980px) {
        .rtl .et-l--footer .et_pb_row_1-4_1-4 > .et_pb_column.et_pb_column_1_4,
        .rtl .et-l--footer .et_pb_row_1-4_1-4_1-2 > .et_pb_column.et_pb_column_1_4,
        .rtl .et-l--footer .et_pb_row_4col > .et_pb_column.et_pb_column_1_4 {
            margin-left: 0;
            margin-right: initial;
        }
        
        .rtl .et_pb_row_1-2_1-4_1-4,
        .rtl .et_pb_row_1-2_1-6_1-6_1-6,
        .rtl .et_pb_row_1-4_1-4,
        .rtl .et_pb_row_1-4_1-4_1-2,
        .rtl .et_pb_row_1-5_1-5_3-5,
        .rtl .et_pb_row_1-6_1-6_1-6,
        .rtl .et_pb_row_1-6_1-6_1-6_1-2,
        .rtl .et_pb_row_1-6_1-6_1-6_1-6,
        .rtl .et_pb_row_3-5_1-5_1-5,
        .rtl .et_pb_row_4col,
        .rtl .et_pb_row_5col,
        .rtl .et_pb_row_6col {
            flex-direction: column-reverse;
        }
    }
    /* Contact Form RTL Fixes */
    .rtl .et_pb_contact .et_pb_contact_field_half {
        float: left !important;
    }
    /* Button RTL Fixes */
    .rtl button.et_pb_contact_submit {
        margin-left: 50px;
        margin-right: initial;
    }
CSS
);
/* === END OF CSS CODE === */

        wp_add_inline_script('dstweaks-inline-script', 'var dstweaksRtl = ' . wp_json_encode(array(
            'isRtl' => is_rtl(),
            'lang' => get_locale(),
            'i18n' => array(
                'direction' => is_rtl() ? 'rtl' : 'ltr',
                'textAlign' => is_rtl() ? 'right' : 'left'
            )
        )) . ';' .
/* === JS CODE STARTS HERE === */'
    (function($) {
        "use strict";

        // Helper function to detect if element contains Persian text
        function hasPersianText(text) {
            return /[\u0600-\u06FF]/.test(text);
        }

        // RTL fixes initialization
        function initRtlFixes() {
            if (!dstweaksRtl.isRtl) return;

            $("input[type=\'text\'], textarea").each(function() {
                $(this).on("input", function() {
                    var $this = $(this);
                    if (hasPersianText($this.val())) {
                        $this.css("direction", "rtl");
                        $this.css("text-align", "right");
                    } else {
                        $this.css("direction", "ltr");
                        $this.css("text-align", "left");
                    }
                });
            });

            $(".rtl .number").each(function() {
                var $this = $(this);
                var text = $this.text();
                if (/[\d.]/.test(text)) {
                    $this.css("direction", "ltr");
                    $this.css("display", "inline-block");
                }
            });

            $("body.rtl").attr("aria-label", dstweaksRtl.i18n.direction);
        }

        $(document).ready(initRtlFixes);

    })(jQuery);
'/* === END OF JS CODE === */, 'before');
    }
 /* === PHP CODE STARTS HERE === */
        public function dstweaks_init_functions() { //Do not remove this function
            add_filter('the_content', array($this, 'dstweaks_fix_persian_characters'));
            add_filter('the_title', array($this, 'dstweaks_fix_persian_characters'));
            add_filter('comment_text', array($this, 'dstweaks_fix_persian_characters'));
            
            add_filter('body_class', array($this, 'dstweaks_add_rtl_body_class'));
            add_filter('comment_text', array($this, 'dstweaks_add_text_direction_attribute'));
            add_filter('the_content', array($this, 'dstweaks_add_text_direction_attribute'));
        }

        public function dstweaks_fix_persian_characters($content) {
            $arabic_chars = array('ي', 'ك');
            $persian_chars = array('ی', 'ک');
            return str_replace($arabic_chars, $persian_chars, $content);
        }

        public function dstweaks_add_rtl_body_class($classes) {
            if (is_rtl()) {
                $classes[] = 'rtl';
            }
            return $classes;
        }

        public function dstweaks_add_text_direction_attribute($content) {
            return '<div dir="auto">' . $content . '</div>';
        }
/* === END OF PHP CODE === */    
}

if (class_exists('DSTweaks_PersianRTLFixes_Module') && !isset($GLOBALS['dstweaks_modules']['persianRTLFixes']['obj'])) {
    $GLOBALS['dstweaks_modules']['persianRTLFixes']['obj'] = new DSTweaks_PersianRTLFixes_Module();
} else 
    $GLOBALS['dstweaks_modules']['persianRTLFixes']['errors'] = "Instantiation failed. Couldn\'t find the required class.";