<?php
/**
 * Module Name: Dynamic Gallery
 * Description: A module for showing a gallery using a custom field containing comma-separated image IDs
 * Version: 4.1.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

class DSTweaks_Divi_Module_DynamicGallery extends ET_Builder_Module_Gallery {
	public $vb_support = 'on';
	
	function init() {
		parent::init();
		$this->name       = esc_html__( 'DST Dynamic Gallery', 'dstweaks' );
		$this->plural     = esc_html__( 'DST Dynamic Galleries', 'dstweaks' );
	}

	function get_fields() {
		$fields = parent::get_fields();

		// Enable dynamic content for gallery_ids
		$fields['gallery_ids']['dynamic_content'] = true;

		return $fields;
	}

    public function render($attrs, $content = null, $render_slug) {
        // Let the original module render first
        $output = parent::render($attrs, $content, $render_slug);

        // Strip the first outer <div> wrapper (generic, flexible)
        if (preg_match('/^<div[^>]*>(.*)<\/div>\s*$/s', $output, $matches)) {
            $output = $matches[1];
        }

        // Replace placeholder image URLs like http://867 or https://867
        $output = preg_replace_callback(
            '#https?://(\d+)\b#',
            function ($matches) {
                $attachment_id = intval($matches[1]);
                $url = wp_get_attachment_url($attachment_id);
                return $url ?: $matches[0];
            },
            $output
        );

        return $output;
    }
}