<?php
/**
 * Module Name: DSTweaks New Divi Modules
 * Description: A module for adding/enhancing Divi builder modules.
 * Version: 2.1.1
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://dsdev.ir
 * License: GPL2
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dstweaks
 */

// Security: Prevent direct file access
defined('ABSPATH') || exit;

$GLOBALS['dstweaks_diviModules'] = array();

class DSTweaks_DiviModules_Module {
    private $modules_dir;
    private $manifest_file;

    public function __construct() {
        if ( $this -> dstweaks_is_divi_builder_available() ) {
            $this -> modules_dir = plugin_dir_path(__FILE__);
            $this -> manifest_file = $this -> modules_dir . 'diviModulesManifest.json';

            if ( $this -> dstweaks_populate_modules() )
                $this -> dstweaks_load_modules();
        }
    }

    public function dstweaks_is_divi_builder_available() {
        $theme = wp_get_theme();
        if ( !function_exists('is_plugin_active') ) {
                require_once ABSPATH . 'wp-admin/includes/plugin.php';
            }
            return $theme->get('Name') === 'Divi' || is_plugin_active('et-builder/et-builder.php');
    }

    private function dstweaks_populate_modules() {
        if (!is_file($this -> manifest_file)) {
            $GLOBALS['dstweaks_modules']['diviModules']['errors'] = __('Manifest file is missing.', 'dstweaks');
            return false;
        }

        $manifest_content = file_get_contents($this -> manifest_file);
        if ($manifest_content === false || !is_string($manifest_content)) {
            $GLOBALS['dstweaks_modules']['diviModules']['errors'] = __('Unable to read manifest file.', 'dstweaks');
            return false;
        }

        $manifest = json_decode($manifest_content, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $GLOBALS['dstweaks_modules']['diviModules']['errors'] = __('Invalid manifest format.', 'dstweaks');;
            return false;
        }

        foreach ($manifest as $module => $details) {
            $module_path = realpath($this -> modules_dir . $module . DIRECTORY_SEPARATOR . $module . '.php');
            $module_data = array(
                'name' => ucfirst($module) . ' Module',
                'description' => '',
                'version' => '',
                'status' => 'inactive',
                'errors' => '',
                'messages' => array()
            );
    
            if ($module_path !== false && strpos($module_path, realpath($this -> modules_dir)) === 0) {
                $file_data = get_file_data($module_path, array('Version' => 'Version', 'Description' => 'Description'));
                $module_data['description'] = $file_data['Description'];
                $module_data['version'] = $file_data['Version'];
            } else {
                $module_data['status'] = 'external_errors';
                $module_data['errors'] = 'invalid_path';
                $error_message = sprintf(
                    /* Translators: 1: Module Name, 2: Exptected Module Path */
                    __('Module file is missing for %1$s: %2$s.', 'dstweaks'), 
                    $module,
                    $module_path
                );
                error_log('DEBUG: Module file is missing for ' . $module . ': ' . $module_path);
                $GLOBALS['dstweaks_modules']['diviModules']['errors'] = $error_message;
                $GLOBALS['dstweaks_modules']['diviModules']['messages'][]= [
                    'type' => 'error',
                    'message' => $error_message
                ];
            }
    
            $GLOBALS['dstweaks_diviModules'][$module] = $module_data;
        }
        return true;
    }

    private function dstweaks_load_modules() {
        foreach ($GLOBALS['dstweaks_diviModules'] as $module => &$module_data) {
            $module_path = realpath($this -> modules_dir . $module . DIRECTORY_SEPARATOR . $module . '.php');
            $details = json_decode(file_get_contents($this -> manifest_file), true)[$module];

            error_log('DEBUG: Loading ' . $module . ' from ' . $module_path);
            if ($module_data['version'] !== $details['version']) {
                $module_data['errors'] = 'version_mismatch';
                $module_data['status'] = 'external_error';
                $error_message = sprintf(
                    /* Translators: 1: Module Name, 2: File Version, 3: Expected File Version */
                    __('Version mismatch for %1$s: file=%2$s, expected=%3$s.', 'dstweaks'), 
                    $module, 
                    $module_data['version'], 
                    $details['version']
                );
                error_log('DEBUG: Version mismatch for ' . $module . ': file=' . $module_data['version'] . ', expected=' . $details['version']);
                $GLOBALS['dstweaks_modules']['diviModules']['errors'] = $error_message;
                $GLOBALS['dstweaks_modules']['diviModules']['messages'][] = [
                    'type' => 'error',
                    'message' => $error_message
                ];
                continue;
            }

            if (!empty($details['manifest_hash'])) {
                $manifest_file_name = strtolower($module) . 'Manifest.json';
                $manifest_file_path = $this -> modules_dir . $module . DIRECTORY_SEPARATOR . $manifest_file_name;
                if (!is_file($manifest_file_path)) {
                    $module_data['errors'] = 'manifest_file_missing';
                    $module_data['status'] = 'external_errors';
                    $error_message = sprintf(
                        /* Translators: 1: Module Name, 2: Expected Manifest File Path */
                        __('Manifest file is missing for %1$s: %2$s.', 'dstweaks'), 
                        $module, 
                        $manifest_file_path
                    );
                    error_log('DEBUG: Manifest file is missing for ' . $module . ': ' . $manifest_file_path);
                    $GLOBALS['dstweaks_modules']['diviModules']['errors'] = $error_message;
                    $GLOBALS['dstweaks_modules']['diviModules']['messages'][]= [
                        'type' => 'error',
                        'message' => $error_message
                    ];
                    continue;
                }
                $manifest_hash = hash_file('sha256', $manifest_file_path);
                if ($manifest_hash !== $details['manifest_hash']) {
                    $module_data['errors'] = 'manifest_hash_mismatch';
                    $module_data['status'] = 'external_errors';
                    $error_message = sprintf(
                        /* Translators: 1: Module Name, 2: File Hash, 3: Expected File Hash */
                        __('Manifest hash mismatch for  %1$s: file=%2$s expected=%3$s.', 'dstweaks'), 
                        $module, 
                        $manifest_hash,
                        $details['manifest_hash']
                    );
                    error_log('DEBUG: Manifest hash mismatch for ' . $module . ': file=' . $manifest_hash . ', expected=' . $details['manifest_hash']);
                    $GLOBALS['dstweaks_modules']['diviModules']['errors'] = $error_message;
                    $GLOBALS['dstweaks_modules']['diviModules']['messages'][]= [
                        'type' => 'error',
                        'message' => $error_message
                    ];
                    continue;
                }
            }

            if (is_file($module_path)) {
                $file_hash = hash_file('sha256', $module_path);
                if ($file_hash === $details['hash']) {
                    if ($module_data['errors']) {
                        $module_data['status'] = 'external_error';
                        $error_message = sprintf(
                            /* Translators: 1: Module Name, 2: Error Message */
                            __('Skipping %1$s due to error: %2$s.', 'dstweaks'), 
                            $module, 
                            $module_data['errors']
                        );
                        error_log('DEBUG: Skipping ' . $module . ' due to error: ' . $module_data['errors']);
                        $GLOBALS['dstweaks_modules']['diviModules']['errors'] = $error_message;
                        $GLOBALS['dstweaks_modules']['diviModules']['messages'][]= [
                            'type' => 'error',
                            'message' => $error_message
                        ];
                        continue;
                    }
                    $module_class_name = 'DSTweaks_Divi_Module_' . $module;
                    add_action('et_builder_ready', function() use ($module, $module_path, $module_class_name, &$module_data) {
                        require_once $module_path;
                        if (!class_exists($module_class_name))
                            $GLOBALS['dstweaks_diviModules'][$module]['errors'] = "Instantiation failed. Couldn\'t find the required class.";
                        else {
                            $GLOBALS['ET_Builder_Module_' . $module_class_name] = new $module_class_name();
                            $module_data['status'] = 'active';
                        }
                    });
                    // Check for internal errors after loading
                    if ($module_data['errors']) {
                        $module_data['status'] = 'internal_error';
                        $error_message = sprintf(
                            /* Translators: 1: Module Name, 2: Error Message */
                            __('Divi Module %1$s is having internal error: %2$s', 'dstweaks'), 
                            $module, 
                            $module_data['errors']
                        );
                        error_log('DEBUG: Module ' . $module . ' is having internal error: ' . $module_data['errors']);
                        $GLOBALS['dstweaks_modules']['diviModules']['errors'] = $error_message;
                        $GLOBALS['dstweaks_modules']['diviModules']['messages'][]= [
                            'type' => 'error',
                            'message' => $error_message
                        ];
                        continue;
                    } else {
                        $GLOBALS['dstweaks_modules']['diviModules']['messages'][]= [
                            'type' => 'good',
                            'message' => sprintf(
                                /* Translators: 1: Module Name, 2: Module Version, 3: Module Description */
                                __('Divi Module %1$s v%2$s:<br>%3$s<br>', 'dstweaks'), 
                                $module,
                                $module_data['version'],
                                $module_data['description']
                            )
                        ];
                    }
                } else {
                    $module_data['status'] = 'external_error';
                    $module_data['errors'] = 'hash_mismatch';
                    $error_message = sprintf(
                        /* Translators: 1: Module Name */
                        __('Hash mismatch for %1$s.', 'dstweaks'), 
                        $module
                    );
                    error_log('DEBUG: Hash mismatch for ' . $module . '');
                    $GLOBALS['dstweaks_modules']['diviModules']['errors'] = $error_message;
                    $GLOBALS['dstweaks_modules']['diviModules']['messages'][]= [
                        'type' => 'error',
                        'message' => $error_message
                    ];
                    continue;
                }
            } else {
                $module_data['status'] = 'external_errors';
                $module_data['errors'] = 'file_missing';
                $error_message = sprintf(
                    /* Translators: 1: Module Name, 2: Exptected Module Path */
                    __('Module file is  missing for %1$s: %2$s.', 'dstweaks'), 
                    $module,
                    $module_path
                );
                error_log('DEBUG: Module file is missing for ' . $module . ': ' . $module_path);
                $GLOBALS['dstweaks_modules']['diviModules']['errors'] = $error_message;
                $GLOBALS['dstweaks_modules']['diviModules']['messages'][]= [
                    'type' => 'error',
                    'message' => $error_message
                ];
                continue;
            }
        }
    }
}

function dstweaks_is_divi_builder_available() {
    $theme = wp_get_theme();
    if ( !function_exists('is_plugin_active') ) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }
        return $theme->get('Name') === 'Divi' || is_plugin_active('et-builder/et-builder.php');
}

if (class_exists('DSTweaks_DiviModules_Module') && !isset($GLOBALS['dstweaks_modules']['diviModules']['obj'])) {
    if (!dstweaks_is_divi_builder_available()) {
        $GLOBALS['dstweaks_modules']['diviModules']['messages'][] = [
            'type' => 'warning',
            'message' => __('Couldn\'t find Divi Builder.', 'dstweaks')
        ];
    }
    $GLOBALS['dstweaks_modules']['diviModules']['obj'] = new DSTweaks_DiviModules_Module();
} else
    $GLOBALS['dstweaks_modules']['diviModules']['errors'] = __('Instantiation failed. Couldn\'t find the required class.', 'dstweaks');
